/**
 * オウンドメディアLP専用JavaScript
 */

(function() {
    'use strict';

    // DOM読み込み完了後に実行
    document.addEventListener('DOMContentLoaded', function() {
        
        // スマホ版での提携企業ロゴ無限スクロール初期化
        initPartnerLogosScroll();
        
        // スムーススクロール初期化
        initSmoothScroll();
        
        // FAQアコーディオン初期化（将来実装用）
        initFAQAccordion();
        
    });

    /**
     * 提携企業ロゴの無限スクロール初期化
     */
    function initPartnerLogosScroll() {
        const partnersSection = document.querySelector('.owned-media__partners');
        const logoContainer = document.querySelector('.owned-media__partners-logos');
        
        if (!partnersSection || !logoContainer) return;
        
        // スマホ版でのみ実行
        if (window.innerWidth < 768) {
            // ロゴを複製して無限スクロール効果を作成
            const logos = logoContainer.innerHTML;
            logoContainer.innerHTML = logos + logos;
            
            // CSS Animationは既にSCSSで定義済み
            logoContainer.classList.add('infinite-scroll');
        }
        
        // リサイズ時の処理
        window.addEventListener('resize', debounce(function() {
            if (window.innerWidth >= 768) {
                // PC版：複製されたロゴを削除
                const logoItems = logoContainer.querySelectorAll('.owned-media__partners-logo-item');
                if (logoItems.length > 3) {
                    for (let i = 3; i < logoItems.length; i++) {
                        logoItems[i].remove();
                    }
                }
                logoContainer.classList.remove('infinite-scroll');
            } else {
                // スマホ版：ロゴを複製
                const logoItems = logoContainer.querySelectorAll('.owned-media__partners-logo-item');
                if (logoItems.length === 3) {
                    const logos = logoContainer.innerHTML;
                    logoContainer.innerHTML = logos + logos;
                }
                logoContainer.classList.add('infinite-scroll');
            }
        }, 250));
    }

    /**
     * スムーススクロール初期化
     */
    function initSmoothScroll() {
        const ctaButtons = document.querySelectorAll('.owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper');
        
        ctaButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                // 実際のフォームが実装されるまでは、お問い合わせセクションへスクロール
                const contactSection = document.querySelector('.section_contact');
                if (contactSection) {
                    e.preventDefault();
                    contactSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * FAQアコーディオン初期化（将来実装用）
     */
    function initFAQAccordion() {
        const faqItems = document.querySelectorAll('.owned-media__faq-item');
        
        faqItems.forEach(function(item) {
            const question = item.querySelector('.owned-media__faq-question');
            const answer = item.querySelector('.owned-media__faq-answer');
            
            if (question && answer) {
                question.addEventListener('click', function() {
                    const isOpen = item.classList.contains('is-open');
                    
                    // 他のFAQを閉じる
                    faqItems.forEach(function(otherItem) {
                        otherItem.classList.remove('is-open');
                        const otherAnswer = otherItem.querySelector('.owned-media__faq-answer');
                        if (otherAnswer) {
                            otherAnswer.style.maxHeight = '0';
                        }
                    });
                    
                    // クリックされたFAQの開閉
                    if (!isOpen) {
                        item.classList.add('is-open');
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                    }
                });
            }
        });
    }

    /**
     * パフォーマンス最適化：debounce関数
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 要素が画面に表示されたときのアニメーション（将来実装用）
     */
    function initScrollAnimations() {
        const animationElements = document.querySelectorAll('.owned-media__services-item, .owned-media__merits-item, .owned-media__support-image-step');
        
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        animationElements.forEach(function(element) {
            observer.observe(element);
        });
    }

    /**
     * モバイルデバイス検出
     */
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * タッチデバイス用の最適化
     */
    function initTouchOptimizations() {
        if (isMobileDevice()) {
            // タッチデバイス用のクラスを追加
            document.documentElement.classList.add('touch-device');
            
            // iOS Safariでの100vh問題を解決
            const setVH = function() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', vh + 'px');
            };
            
            setVH();
            window.addEventListener('resize', debounce(setVH, 250));
        }
    }

    // タッチ最適化を初期化
    initTouchOptimizations();

})();